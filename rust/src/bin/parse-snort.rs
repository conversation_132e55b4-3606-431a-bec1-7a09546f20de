use std::collections::{HashMap, HashSet};
use std::fs::{File, create_dir_all, remove_dir_all};
use std::io::{BufRead, BufReader};
use std::path::Path;

use clap::Parser;
use dashmap::DashMap;
use mysql::{
    OptsBuilder,
    prelude::Queryable,
    Pool,
};
use rayon::iter::{
    IntoParallelRefIterator,
    ParallelIterator,
    // ParallelBridge,  // optional if you use `.par_bridge()`
};
use serde_json;

use eterna::utils_classes::{
    SnortConfig,
    SnortParser,
    MYSQLConfig,
    MYSQLValue,
};

use eterna::utils::{
    create_name_of_database,
};

use eterna::utils_parsers::{
    ConfigType,
    parse_ln,
};

// performance constants for large file processing
const FILE_BUFFER_SIZE: usize = 8 * 1024 * 1024;  // 8MB buffer for optimal I/O performance

#[derive(Parser, Debug)]
#[command(author, version, about)]
struct Args {
    #[arg(long = "source-log")]
    source_log: String,
    // /FOO/BAR/BAZ/2025-06-13--Fri.log

    #[arg(long = "log-date")]
    log_date: String,
    // 2020-01-02

    #[arg(long = "already-accomplished", num_args = 0..)]
    already_accomplished: Vec<String>,
    // [] OR ["Sensor-1", "Sensor-2", ...]

    #[arg(long = "sensor-list-of-names", num_args = 1..)]
    sensor_list_of_names: Vec<String>,
    // ["Sensor-1", "Sensor-2", "Sensor-3", ...]

    #[arg(long = "sensor-list-of-names-and-addresses", num_args = 1..)]
    sensor_list_of_names_and_addresses: Vec<String>,
    // ["Sensor-1", "***********", "Sensor-2", "***********", ...]

    #[arg(long = "sensor-dict-of-addresses-and-names")]
    sensor_dict_of_addresses_and_names: String,
    // "{\"***********\": \"Sensor-1\", \"***********\": \"Sensor-2\", ...}"

    #[arg(long = "force", num_args = 0)]
    #[arg(default_value_t = false)]
    force: bool,
    // true/false
}

fn trim_newlines(line: &mut String) {
    while line.ends_with('\n') || line.ends_with('\r') {
        line.pop();
    }
}

fn main() {
    let args = Args::parse();

    // println!("Source log: {:?}", args.source_log);
    // println!("Log date: {:?}", args.log_date);
    // println!("Already accomplished: {:?}", args.already_accomplished);
    // println!("Sensor list of names: {:?}", args.sensor_list_of_names);
    // println!("Sensor list of names and addresses: {:?}", args.sensor_list_of_names_and_addresses);
    // println!("Sensor dict of addresses and names: {:?}", args.sensor_dict_of_addresses_and_names);

    // string -> dict
    let sensor_dict_of_addresses_and_names: HashMap<String, String> =
        serde_json::from_str(&args.sensor_dict_of_addresses_and_names)
            .expect("Failed to parse sensor_dict_of_addresses_and_names");

    // list -> set for O(1) lookup
    let already_accomplished: HashSet<String> =
        args.already_accomplished.into_iter().collect();

    // create dictionary of instances
    let sensor_names_and_instances: DashMap<String, SnortParser> = DashMap::new();
    for s_n in &args.sensor_list_of_names {
        sensor_names_and_instances.insert(
            s_n.clone(),
            SnortParser::new(
                SnortConfig::SLUG.value_string(),
                args.log_date.to_string(),
                s_n.to_string(),
            ),
        );
    }

    // __PARSING__ start

    // open file with larger buffer for better I/O performance
    let file = File::open(&args.source_log)
        .expect(&format!("Failed to open source log: {}", args.source_log));
    let mut reader = BufReader::with_capacity(FILE_BUFFER_SIZE, file);

    println!("parsing...");

    // process file in chunks to avoid loading entire file into memory
    let pool_chunksize = if let eterna::utils_classes::MYSQLValue::Int(size) = MYSQLConfig::POOL_CHUNKSIZE.value() {
        size
    } else {
        panic!("Error getting pool_chunksize from MYSQLConfig");
    };

    loop {
        let mut chunk = Vec::with_capacity(pool_chunksize);

        // read chunk of lines
        for _ in 0..pool_chunksize {
            let mut line = String::new();
            match reader.read_line(&mut line) {
                Ok(0) => break, // EOF
                Ok(_) => {
                    // remove newline character
                    trim_newlines(&mut line);
                    chunk.push(line);
                }
                Err(e) => panic!("Error reading line: {}", e),
            }
        }

        if chunk.is_empty() {
            // EOF reached
            break;
        }

        // process chunk in parallel, aggregate locally to avoid DashMap contention
        let local_results: HashMap<String, Vec<Vec<String>>> = chunk
            .par_iter()
            .map(|line| {
                let (sensor_name, parsed_ln) = parse_ln(
                    line.trim(),
                    ConfigType::Snort,
                    &args.sensor_list_of_names_and_addresses,
                    &sensor_dict_of_addresses_and_names,
                );

                // check if sensor is already accomplished (O(1) lookup)
                if let Some(ref name) = sensor_name {
                    if already_accomplished.contains(name) {
                        return None;
                    }
                }

                match (sensor_name, parsed_ln) {
                    (Some(name), Some(row)) => Some((name, row)),
                    _ => None,
                }
            })
            .filter_map(|x| x)
            .fold(HashMap::new, |mut acc, (name, row)| {
                acc.entry(name).or_insert_with(Vec::new).push(row);
                acc
            })
            .reduce(HashMap::new, |mut acc1, acc2| {
                for (k, mut v) in acc2 {
                    acc1.entry(k).or_insert_with(Vec::new).append(&mut v);
                }
                acc1
            });

        // collect results into sensor instances
        for (name, rows) in local_results {
            if let Some(mut instance) = sensor_names_and_instances.get_mut(&name) {
                instance.rows.extend(rows);
            }
        }

        // println!("Processed {} lines in {:?}", chunk.len(), start.elapsed());
    }

    // __TODO__ temporary
    println!("\nLines parsed per sensor:");
    for entry in sensor_names_and_instances.iter() {
        println!("  {}: {} lines", entry.key(), entry.value().rows.len());
    }

    // __PARSING__ end

    // __DB_HANDLING__ start

    for entry in sensor_names_and_instances.iter() {
        let sensor_name = entry.key();
        let instance = entry.value();

        let dest_dir          = format!("{}/{}/{}", SnortConfig::get_logs_parsed_dir(), sensor_name, args.log_date);
        let accomplished_file = format!("{}/{}-accomplished.log", dest_dir, args.log_date);
        let log_file          = format!("{}/{}.log", dest_dir, args.log_date);

        let database_name = create_name_of_database(&SnortConfig::SLUG.value_string(), &args.log_date, sensor_name);

        // ################################################

        // remove and/or create dest_dir
        if Path::new(&dest_dir).exists() {
            let mut should_rm_dest_dir = false;

            if args.force {
                should_rm_dest_dir = true;
            } else {
                if Path::new(&accomplished_file).exists() {
                    println!("{} for sensor {} is already parsed. skipping", args.log_date, sensor_name);
                    continue;
                } else {
                    should_rm_dest_dir = true;
                }
            };

            if should_rm_dest_dir {
                println!("removing {}", dest_dir);
                if let Err(e) = remove_dir_all(&dest_dir) {
                    eprintln!("Error removing directory {}: {}", dest_dir, e);
                }
                println!("creating {}", dest_dir);
                if let Err(e) = create_dir_all(&dest_dir) {
                    eprintln!("Error creating directory {}: {}", dest_dir, e);
                }
            }
        } else {
            println!("creating {}", dest_dir);
            if let Err(e) = create_dir_all(&dest_dir) {
                eprintln!("Error creating directory {}: {}", dest_dir, e);
            }
        }

        // ################################################

        // START __inserting_into_dbs__

        let mysql_host = match MYSQLConfig::MYSQL_HOST.value() {
            MYSQLValue::Str(host) => host,
            _ => panic!("Error getting MYSQL_HOST"),
        };
        let mysql_user = match MYSQLConfig::MYSQL_MASTER.value() {
            MYSQLValue::Str(user) => user,
            _ => panic!("Error getting MYSQL_MASTER"),
        };
        let mysql_password = match MYSQLConfig::MYSQL_MASTER_PASSWD.value() {
            MYSQLValue::Str(password) => password,
            _ => panic!("Error getting MYSQL_MASTER_PASSWD"),
        };

        let db_opts = OptsBuilder::new()
            .ip_or_hostname(Some(mysql_host))
            .user(Some(mysql_user))
            .pass(Some(mysql_password));

        // drop/create database
        match Pool::new(db_opts) {
            Ok(pool) => {
                match pool.get_conn() {
                    Ok(mut conn) => {
                        println!("dropping database {}", database_name);
                        if let Err(e) = conn.query_drop(format!("DROP DATABASE IF EXISTS {};", database_name)) {
                            eprintln!("Error dropping database {}: {}", database_name, e);
                        }

                        println!("creating database {}", database_name);
                        if let Err(e) = conn.query_drop(format!("CREATE DATABASE {};", database_name)) {
                            eprintln!("Error creating database {}: {}", database_name, e);
                        }
                    }
                    Err(e) => {
                        eprintln!("Error getting database connection: {}", e);
                    }
                }
            }
            Err(e) => {
                eprintln!("Error creating database pool: {}", e);
            }
        }

        // __DB_HANDLING__ end
    }










    println!("\nParser Finished Successfully");
}
