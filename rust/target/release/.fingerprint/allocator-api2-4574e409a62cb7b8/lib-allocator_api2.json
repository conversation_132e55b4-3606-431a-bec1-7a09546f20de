{"rustc": 2697959624369812960, "features": "[\"alloc\"]", "declared_features": "[\"alloc\", \"default\", \"fresh-rust\", \"nightly\", \"serde\", \"std\"]", "target": 5388200169723499962, "profile": 4067574213046180398, "path": 3750108180398803579, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/allocator-api2-4574e409a62cb7b8/dep-lib-allocator_api2", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}