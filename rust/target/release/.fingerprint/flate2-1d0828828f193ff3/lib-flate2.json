{"rustc": 2697959624369812960, "features": "[\"any_impl\", \"any_zlib\", \"libz-sys\", \"zlib\"]", "declared_features": "[\"any_impl\", \"any_zlib\", \"cloudflare-zlib-sys\", \"cloudflare_zlib\", \"default\", \"libz-ng-sys\", \"libz-rs-sys\", \"libz-sys\", \"miniz-sys\", \"miniz_oxide\", \"rust_backend\", \"zlib\", \"zlib-default\", \"zlib-ng\", \"zlib-ng-compat\", \"zlib-rs\"]", "target": 6173716359330453699, "profile": 2040997289075261528, "path": 13790030518634350832, "deps": [[7312356825837975969, "crc32fast", false, 16842711964590669799], [17022423707615322322, "libz_sys", false, 15627281429724453229]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/flate2-1d0828828f193ff3/dep-lib-flate2", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}