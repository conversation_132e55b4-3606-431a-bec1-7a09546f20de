{"rustc": 2697959624369812960, "features": "[\"std\", \"unicode-perl\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 1369601567987815722, "path": 345022235489212113, "deps": [[555019317135488525, "regex_automata", false, 18381610233295044000], [9408802513701742484, "regex_syntax", false, 8764013932072804677]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/regex-085d392129666384/dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}