{"rustc": 2697959624369812960, "features": "[\"default\"]", "declared_features": "[\"aws-lc\", \"bindgen\", \"default\", \"unstable_boringssl\", \"v101\", \"v102\", \"v110\", \"v111\", \"vendored\"]", "target": 17474193825155910204, "profile": 2040997289075261528, "path": 10876064452377734601, "deps": [[2828590642173593838, "cfg_if", false, 6946526656978218139], [3722963349756955755, "once_cell", false, 18245732150123265057], [4684437522915235464, "libc", false, 2213938177650776263], [6635237767502169825, "foreign_types", false, 8772094700283438262], [7896293946984509699, "bitflags", false, 15501179784294473747], [8607891082156236373, "build_script_build", false, 11116117482473714787], [9070360545695802481, "ffi", false, 7504449441491458765], [10099563100786658307, "openssl_macros", false, 10111086526734711880]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/openssl-1405f80cdb76a193/dep-lib-openssl", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}