{"rustc": 2697959624369812960, "features": "[]", "declared_features": "[\"ahash\", \"alloc\", \"allocator-api2\", \"compiler_builtins\", \"core\", \"default\", \"equivalent\", \"inline-more\", \"nightly\", \"raw\", \"rayon\", \"rkyv\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 9101038166729729440, "profile": 2040997289075261528, "path": 722092113499539770, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/hashbrown-ae4769feca31ecdf/dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}