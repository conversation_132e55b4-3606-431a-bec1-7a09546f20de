{"rustc": 2697959624369812960, "features": "[\"allocator-api2\", \"default\", \"default-hasher\", \"equivalent\", \"inline-more\", \"raw-entry\"]", "declared_features": "[\"alloc\", \"allocator-api2\", \"core\", \"default\", \"default-hasher\", \"equivalent\", \"inline-more\", \"nightly\", \"raw-entry\", \"rayon\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 13796197676120832388, "profile": 2040997289075261528, "path": 1141861223084665546, "deps": [[5230392855116717286, "equivalent", false, 4677610364250684323], [9150530836556604396, "allocator_api2", false, 1083565329735081303], [10842263908529601448, "<PERSON><PERSON><PERSON>", false, 10246407648110705904]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/hashbrown-d15b30e175f421d4/dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}