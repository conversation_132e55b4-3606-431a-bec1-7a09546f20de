{"rustc": 2697959624369812960, "features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"visit\", \"visit-mut\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 1369601567987815722, "path": 6393180312218821258, "deps": [[1988483478007900009, "unicode_ident", false, 8537552379701141993], [3060637413840920116, "proc_macro2", false, 1650941221014339652], [17990358020177143287, "quote", false, 6888637619886377392]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/syn-3cab4588a23267d6/dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}