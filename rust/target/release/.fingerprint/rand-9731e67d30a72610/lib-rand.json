{"rustc": 2697959624369812960, "features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"rand_chacha\", \"std\", \"std_rng\"]", "declared_features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"log\", \"min_const_gen\", \"nightly\", \"packed_simd\", \"rand_chacha\", \"serde\", \"serde1\", \"simd_support\", \"small_rng\", \"std\", \"std_rng\"]", "target": 8827111241893198906, "profile": 2040997289075261528, "path": 14042340326931454860, "deps": [[1573238666360410412, "rand_chacha", false, 16212905539060256981], [4684437522915235464, "libc", false, 2213938177650776263], [18130209639506977569, "rand_core", false, 1843275782339679649]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/rand-9731e67d30a72610/dep-lib-rand", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}