{"rustc": 2697959624369812960, "features": "[\"runtime\"]", "declared_features": "[\"__cli\", \"__testing_only_extra_assertions\", \"__testing_only_libclang_16\", \"__testing_only_libclang_9\", \"default\", \"experimental\", \"logging\", \"prettyplease\", \"runtime\", \"static\"]", "target": 3886691532138051063, "profile": 496227967493411517, "path": 12845816733367352856, "deps": [[950716570147248582, "cexpr", false, 2023288793404600352], [1711752468582068132, "build_script_build", false, 13668511780385128867], [3060637413840920116, "proc_macro2", false, 1650941221014339652], [3317542222502007281, "itertools", false, 10765820272678033118], [4885725550624711673, "clang_sys", false, 8813426814896527880], [4974441333307933176, "syn", false, 16685863913465580410], [7896293946984509699, "bitflags", false, 1083579365435039196], [8410525223747752176, "shlex", false, 16533860466906902567], [9451456094439810778, "regex", false, 2923401446399943758], [17990358020177143287, "quote", false, 6888637619886377392], [18335655851112826545, "rustc_hash", false, 10824196903281475667]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/bindgen-fd1b76bd207e2964/dep-lib-bindgen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}