{"rustc": 2697959624369812960, "features": "[\"alloc\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"debug\", \"default\", \"simd\", \"std\", \"unstable-doc\", \"unstable-recover\"]", "target": 13376497836617006023, "profile": 5451394256983680347, "path": 11891188846846818356, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/winnow-5ad19e643e076225/dep-lib-winnow", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}